import os


RAW_DIR = "artifacts/raw"
RAW_FILE_PATH = os.path.join(RAW_DIR, "raw.csv")
TRAIN_FILE_PATH = os.path.join(RAW_DIR, "train.csv")
TEST_FILE_PATH = os.path.join(RAW_DIR, "test.csv")

CONFIG_PATH = "config/config.yaml"
GCP_SA_KEY_PATH = "YOUR GCP CREDINTIALS/pass the file here"


################ Data Processing #############

PROCESSED_DIR = "artifacts/processed"
PROCESSED_TRAIN_DATA_PATH = os.path.join(PROCESSED_DIR, "processed_train_csv")
PROCESSED_TEST_DATA_PATH = os.path.join(PROCESSED_DIR, "processed_test_csv")


####################### MODEL TRAINING #################
MODEL_OUTPUT_PATH = "artifacts/models/lgbm_model.pkl"