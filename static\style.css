/* General reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Body styling */
body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fa;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
}

/* Container styling */
.container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
}

/* Heading styling */
h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
}

/* Form group styling */
.form-group {
    margin-bottom: 20px;
}

/* Label styling */
label {
    font-size: 1rem;
    color: #555;
    display: block;
    margin-bottom: 8px;
}

/* Input and select field styling */
input[type="number"], select {
    width: 100%;
    padding: 12px;
    font-size: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input[type="number"]:focus, select:focus {
    border-color: #0056b3;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 86, 179, 0.5);
}

/* Button styling */
button {
    width: 100%;
    padding: 14px;
    background-color: #0056b3;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #004494;
}

/* Result section styling */
.result {
    margin-top: 20px;
    text-align: center;
    font-size: 1.2rem;
    padding: 15px;
    border-radius: 6px;
    background-color: #e8f5e9;
    color: #388e3c;
}

.result p {
    font-weight: bold;
}

.result p.cancel {
    color: #d32f2f;
}

.result p.not-cancel {
    color: #388e3c;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 20px;
    }

    h2 {
        font-size: 1.8rem;
    }

    button {
        padding: 12px;
    }
}
